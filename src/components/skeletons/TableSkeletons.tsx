import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

// Resource Table Skeleton
export const ResourceTableSkeleton = ({ columns = 6, rows = 8 }: { columns?: number; rows?: number }) => (
  <div className='animate-pulse'>
    <Table>
      <TableHeader>
        <TableRow>
          {Array(columns).fill(0).map((_, index) => (
            <TableHead key={index}>
              <div className='h-4 bg-muted rounded w-20'></div>
            </TableHead>
          ))}
        </TableRow>
      </TableHeader>
      <TableBody>
        {Array(rows).fill(0).map((_, rowIndex) => (
          <TableRow key={rowIndex} className='hover:bg-muted/50 h-auto'>
            {Array(columns).fill(0).map((_, colIndex) => (
              <TableCell key={colIndex} className='py-3'>
                {colIndex === 0 ? (
                  // Title column - larger content
                  <div className='space-y-2'>
                    <div className='h-4 bg-muted rounded w-3/4'></div>
                    <div className='flex flex-wrap gap-1'>
                      <div className='h-5 bg-muted rounded w-12'></div>
                      <div className='h-5 bg-muted rounded w-16'></div>
                    </div>
                  </div>
                ) : colIndex === 1 ? (
                  // Author column
                  <div className='flex items-center gap-2'>
                    <div className='h-6 w-6 bg-muted rounded-full'></div>
                    <div className='h-4 bg-muted rounded w-20'></div>
                  </div>
                ) : colIndex === 2 ? (
                  // Type column
                  <div className='h-6 bg-muted rounded w-16'></div>
                ) : colIndex === 3 ? (
                  // Engagement column
                  <div className='flex items-center gap-2'>
                    <div className='h-4 bg-muted rounded w-8'></div>
                    <div className='h-4 bg-muted rounded w-8'></div>
                  </div>
                ) : colIndex === 4 ? (
                  // Date column
                  <div className='h-4 bg-muted rounded w-16'></div>
                ) : (
                  // Actions column
                  <div className='flex items-center gap-1'>
                    <div className='h-6 w-6 bg-muted rounded'></div>
                    <div className='h-6 w-6 bg-muted rounded'></div>
                    <div className='h-6 w-6 bg-muted rounded'></div>
                    <div className='h-6 w-6 bg-muted rounded'></div>
                  </div>
                )}
              </TableCell>
            ))}
          </TableRow>
        ))}
      </TableBody>
    </Table>
  </div>
);

// Proposal Table Skeleton
export const ProposalTableSkeleton = ({ rows = 5 }: { rows?: number }) => (
  <div className='animate-pulse'>
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead><div className='h-4 bg-muted rounded w-12'></div></TableHead>
          <TableHead><div className='h-4 bg-muted rounded w-20'></div></TableHead>
          <TableHead><div className='h-4 bg-muted rounded w-16'></div></TableHead>
          <TableHead><div className='h-4 bg-muted rounded w-16'></div></TableHead>
          <TableHead><div className='h-4 bg-muted rounded w-20'></div></TableHead>
          <TableHead><div className='h-4 bg-muted rounded w-16'></div></TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {Array(rows).fill(0).map((_, index) => (
          <TableRow key={index}>
            <TableCell className='font-medium'>
              <div className='h-4 bg-muted rounded w-3/4'></div>
            </TableCell>
            <TableCell>
              <div className='h-4 bg-muted rounded w-20'></div>
            </TableCell>
            <TableCell>
              <div className='h-6 bg-muted rounded w-16'></div>
            </TableCell>
            <TableCell>
              <div className='h-4 bg-muted rounded w-24'></div>
            </TableCell>
            <TableCell>
              <div className='h-4 bg-muted rounded w-16'></div>
            </TableCell>
            <TableCell>
              <div className='flex gap-2'>
                <div className='h-8 w-8 bg-muted rounded'></div>
                <div className='h-8 w-8 bg-muted rounded'></div>
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  </div>
);

// Grid Skeleton for card layouts
export const GridSkeleton = ({ 
  SkeletonComponent, 
  count = 6, 
  columns = 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3' 
}: { 
  SkeletonComponent: React.ComponentType; 
  count?: number; 
  columns?: string;
}) => (
  <div className={`grid ${columns} gap-2`}>
    {Array(count).fill(0).map((_, index) => (
      <SkeletonComponent key={index} />
    ))}
  </div>
);
