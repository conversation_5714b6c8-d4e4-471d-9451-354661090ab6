import { cn } from '@/lib/utils';
import { GlowingEffect } from '@/components/ui/glowing-effect';

// Article Card Skeleton
export const ArticleCardSkeleton = () => (
  <div className='flex flex-col items-start gap-2 rounded-lg border p-3 text-left text-sm max-h-[400px] relative animate-pulse'>
    <GlowingEffect
      disabled={false}
      spread={40}
      glow={true}
      proximity={64}
      inactiveZone={0.01}
      borderWidth={1.5}
    />
    <div className='w-full'>
      <div className='flex gap-2 justify-between w-full items-center mb-2 flex-wrap'>
        {/* Author avatar and name */}
        <div className='flex items-center gap-2'>
          <div className='h-8 w-8 bg-muted rounded-full'></div>
          <div className='h-4 bg-muted rounded w-24'></div>
        </div>
        
        {/* Action buttons */}
        <div className='flex items-center gap-2 flex-wrap'>
          <div className='h-6 w-6 bg-muted rounded'></div>
          <div className='h-6 w-6 bg-muted rounded'></div>
          <div className='h-6 w-6 bg-muted rounded'></div>
          <div className='h-6 w-6 bg-muted rounded'></div>
        </div>
      </div>

      <div className='flex items-start justify-between'>
        <div className='flex items-center gap-2 flex-1'>
          <div className='h-5 bg-muted rounded w-3/4'></div>
        </div>
        <div className='h-3 bg-muted rounded w-20'></div>
      </div>
    </div>
    
    {/* Tags */}
    <div className='flex flex-wrap gap-1'>
      <div className='h-6 bg-muted rounded w-16'></div>
      <div className='h-6 bg-muted rounded w-20'></div>
      <div className='h-6 bg-muted rounded w-14'></div>
    </div>
    
    {/* Bottom actions */}
    <div className='flex justify-end gap-2 py-2 w-full'>
      <div className='h-8 w-8 bg-muted rounded'></div>
      <div className='h-8 w-8 bg-muted rounded'></div>
    </div>
  </div>
);

// GitHub Card Skeleton
export const GithubCardSkeleton = () => (
  <div className='flex flex-col cursor-pointer relative rounded-lg border bg-card text-card-foreground shadow-sm animate-pulse'>
    <GlowingEffect
      disabled={false}
      spread={40}
      glow={true}
      proximity={64}
      inactiveZone={0.01}
      borderWidth={1.5}
    />
    <div className='flex justify-end f-full pt-2'>
      <div className='h-3 bg-muted rounded w-20 mr-auto ml-6'></div>
      <div className='flex gap-1 mr-2'>
        <div className='h-6 w-6 bg-muted rounded'></div>
        <div className='h-6 w-6 bg-muted rounded'></div>
        <div className='h-6 w-6 bg-muted rounded'></div>
        <div className='h-6 w-6 bg-muted rounded'></div>
      </div>
    </div>
    
    <div className='py-4 pl-6'>
      <div className='flex items-center gap-2'>
        <div className='h-8 w-8 bg-muted rounded-full'></div>
        <div className='h-4 bg-muted rounded w-24'></div>
      </div>
    </div>
    
    <div className='p-6'>
      <div className='flex justify-center'>
        <div className='w-full max-w-md h-32 bg-muted rounded-lg'></div>
      </div>
    </div>
    
    <div className='p-6 pt-0 grow'>
      <div className='flex flex-wrap gap-1 mt-auto'>
        <div className='h-6 bg-muted rounded w-16'></div>
        <div className='h-6 bg-muted rounded w-20'></div>
      </div>
    </div>
    
    <div className='flex justify-end gap-2 py-2 w-full px-2'>
      <div className='h-8 w-8 bg-muted rounded'></div>
      <div className='h-8 w-8 bg-muted rounded'></div>
    </div>
  </div>
);

// Video Card Skeleton
export const VideoCardSkeleton = () => (
  <div className='flex flex-col cursor-pointer relative rounded-lg border bg-card text-card-foreground shadow-sm animate-pulse'>
    <GlowingEffect
      disabled={false}
      spread={40}
      glow={true}
      proximity={64}
      inactiveZone={0.01}
      borderWidth={1.5}
    />
    <div className='flex justify-end f-full pt-2'>
      <div className='h-3 bg-muted rounded w-20 mr-auto ml-6'></div>
      <div className='flex gap-1 mr-2'>
        <div className='h-6 w-6 bg-muted rounded'></div>
        <div className='h-6 w-6 bg-muted rounded'></div>
        <div className='h-6 w-6 bg-muted rounded'></div>
        <div className='h-6 w-6 bg-muted rounded'></div>
      </div>
    </div>
    
    <div className='py-4 pl-6'>
      <div className='flex items-center gap-2'>
        <div className='h-8 w-8 bg-muted rounded-full'></div>
        <div className='h-4 bg-muted rounded w-24'></div>
      </div>
    </div>
    
    <div className='p-6'>
      <div className='min-w-[300px] grow'>
        <div className='h-5 bg-muted rounded w-3/4 mb-3'></div>
        <div className='w-full h-[300px] bg-muted rounded'></div>
        <div className='mt-2 h-3 bg-muted rounded w-1/3 ml-auto'></div>
      </div>
    </div>
    
    <div className='p-6 pt-0 grow'>
      <div className='flex flex-wrap gap-1 mt-auto'>
        <div className='h-6 bg-muted rounded w-16'></div>
        <div className='h-6 bg-muted rounded w-20'></div>
      </div>
    </div>
    
    <div className='flex justify-end gap-2 py-2 w-full px-2'>
      <div className='h-8 w-8 bg-muted rounded'></div>
      <div className='h-8 w-8 bg-muted rounded'></div>
    </div>
  </div>
);

// Site Card Skeleton
export const SiteCardSkeleton = () => (
  <div className='group rounded-xl border border-border/50 bg-card/50 backdrop-blur-sm cursor-pointer animate-pulse'>
    <GlowingEffect
      disabled={false}
      spread={40}
      glow={true}
      proximity={64}
      inactiveZone={0.01}
      borderWidth={1.5}
    />
    
    <div className='relative p-6 space-y-4'>
      <div className='space-y-3'>
        <div className='flex items-start justify-between gap-3'>
          <div className='flex-1 min-w-0 space-y-2'>
            <div className='h-6 bg-muted rounded w-3/4'></div>
            <div className='flex items-center gap-2'>
              <div className='h-3.5 w-3.5 bg-muted rounded'></div>
              <div className='h-4 bg-muted rounded w-32'></div>
            </div>
          </div>
          
          <div className='flex items-center gap-1'>
            <div className='h-6 w-6 bg-muted rounded'></div>
            <div className='h-6 w-6 bg-muted rounded'></div>
            <div className='h-6 w-6 bg-muted rounded'></div>
            <div className='h-6 w-6 bg-muted rounded'></div>
          </div>
        </div>
      </div>

      <div className='flex gap-4'>
        <div className='w-20 h-20 bg-muted rounded-lg'></div>
        <div className='flex-1 space-y-2'>
          <div className='h-4 bg-muted rounded w-full'></div>
          <div className='h-4 bg-muted rounded w-5/6'></div>
          <div className='h-4 bg-muted rounded w-4/6'></div>
        </div>
      </div>

      <div className='flex flex-wrap gap-1.5'>
        <div className='h-6 bg-muted rounded w-16'></div>
        <div className='h-6 bg-muted rounded w-20'></div>
        <div className='h-6 bg-muted rounded w-14'></div>
      </div>
      
      <div className='flex justify-end gap-2'>
        <div className='h-8 w-8 bg-muted rounded'></div>
        <div className='h-8 w-8 bg-muted rounded'></div>
      </div>
    </div>
  </div>
);

// Tweet Card Skeleton (improved version)
export const TweetCardSkeleton = () => (
  <div className='flex flex-col transition-all duration-200 relative rounded-xl border border-border/60 bg-background animate-pulse'>
    <GlowingEffect
      disabled={false}
      spread={40}
      glow={true}
      proximity={64}
      inactiveZone={0.01}
      borderWidth={1.5}
    />
    <div className='relative'>
      <div className='p-3 pb-0'>
        <div className='flex items-center space-x-3 mb-4'>
          <div className='h-10 w-10 bg-muted rounded-full'></div>
          <div className='space-y-2 flex-1'>
            <div className='h-4 bg-muted rounded w-1/3'></div>
            <div className='h-3 bg-muted rounded w-1/4'></div>
          </div>
        </div>
        <div className='space-y-2'>
          <div className='h-4 bg-muted rounded w-full'></div>
          <div className='h-4 bg-muted rounded w-5/6'></div>
          <div className='h-4 bg-muted rounded w-4/6'></div>
        </div>
        <div className='mt-4 flex justify-between'>
          <div className='h-6 w-6 bg-muted rounded'></div>
          <div className='h-6 w-6 bg-muted rounded'></div>
          <div className='h-6 w-6 bg-muted rounded'></div>
          <div className='h-6 w-6 bg-muted rounded'></div>
        </div>
      </div>

      <div className='absolute top-2 right-2 flex gap-1 p-1 rounded-full bg-background/80 backdrop-blur-sm border border-border/40'>
        <div className='h-6 w-6 bg-muted rounded'></div>
        <div className='h-6 w-6 bg-muted rounded'></div>
        <div className='h-6 w-6 bg-muted rounded'></div>
      </div>
    </div>

    <div className='p-3 pt-0'>
      <div className='flex flex-wrap gap-1'>
        <div className='h-6 bg-muted rounded w-16'></div>
        <div className='h-6 bg-muted rounded w-20'></div>
      </div>
    </div>
    
    <div className='flex justify-end gap-2 py-2 w-full px-3'>
      <div className='h-8 w-8 bg-muted rounded'></div>
      <div className='h-8 w-8 bg-muted rounded'></div>
    </div>
  </div>
);
