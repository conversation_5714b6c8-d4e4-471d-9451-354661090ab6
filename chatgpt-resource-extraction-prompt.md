# SmartSecHub Resource Extraction Instructions for ChatGPT

## Overview

You are tasked with analyzing cybersecurity-related URLs and extracting structured information to create JSON resources for SmartSecHub. Follow these instructions carefully to generate properly formatted JSON data.

## Your Task

1. Visit each provided URL
2. Read and analyze the content
3. Extract relevant information
4. Generate a JSON array of resources following the specified schema

## JSON Schema Requirements

Each resource object must contain the following fields:

### Required Fields:

- **title** (string): The main title of the content
- **url** (string): The original URL provided
- **createdAt** (string): The date the content was created in `YYYY-MM-DD` format
- **tags** (array): THIS is really important field, analyze the content and add tags that describe the content. Use existing tags - I will provide them to you.
- **type** (string): One of the following values:
  - `"article"` - Blog posts, articles, written content
  - `"video"` - YouTube videos, video tutorials
  - `"tweet"` - Twitter/X posts
  - `"github"` - GitHub repositories, code projects
  - `"papers"` - Research papers, whitepapers, academic content
  - `"sites"` - Websites, tools, platforms

### Optional Fields:
- **customTwitterId** (string): If the author's Twitter ID is not in the database, add it here

### Fields to exclude:
- Do not include `id`, `likesCount`, `dislikesCount`,

## Content Analysis Guidelines

### Title Extraction:

- Use the actual title from the webpage/content
- If no clear title exists
- Keep titles concise but informative
- Remove unnecessary prefixes like "Blog:" or suffixes like "| Company Name"

### Type Classification:

- **article**: Blog posts, tutorials, guides, news articles, documentation
- **video**: Any video content (YouTube, Vimeo, etc.)
- **tweet**: Twitter/X posts and threads
- **github**: GitHub repositories, code samples, open source projects
- **papers**: Academic papers, research documents, whitepapers, technical reports
- **sites**: Tools, platforms, websites, online services, dashboards


## Output Format

Return a valid JSON array with this exact structure:

```json
[
  {
    "title": "Example Security Article Title",
    "url": "https://example.com/article",
    "type": "article",
    "createdAt": <current date>,
    "customTwitterId": <i can provide this>
    "authorId": <I will provide this>
    "tags": []
  },
  {
    "title": "Another Resource Title",
    "url": "https://example.com/video",
    "type": "video",
    "createdAt": <current date>,
    "customTwitterId": <i can provide this>
    "authorId": <I will provide this>
    "tags": []
  }
]
```

## Quality Standards

### Content Relevance:

Only include content related to:

- Cybersecurity
- Information security
- Penetration testing
- Vulnerability research
- Security tools and techniques
- Incident response
- Security architecture
- Compliance and governance
- Privacy and data protection

### Accuracy Requirements:

- Verify the URL is accessible
- Ensure the title matches the actual content
- Confirm the type classification is correct

## Error Handling

If a URL is inaccessible or contains irrelevant content:

- Skip that URL
- Do not include it in the JSON output
- Mention skipped URLs in a separate note after the JSON

## Example Input/Output

**Input URLs:**

- https://owasp.org/www-project-top-ten/
- https://github.com/danielmiessler/SecLists

**Expected Output:**

```json
[
  {
    "title": "OWASP Top 10 Web Application Security Risks",
    "url": "https://owasp.org/www-project-top-ten/",
    "type": "sites",
    "tags": []
  },
  {
    "title": "SecLists - Security Testing Lists",
    "url": "https://github.com/danielmiessler/SecLists",
    "type": "github",
    "tags": []
  }
]
```

## Final Notes

- Always return valid JSON that can be parsed
- Double-check URLs for typos
- Ensure all required fields are present
- Focus on cybersecurity relevance
- Maintain consistent formatting

---

**Instructions for use:** Paste the URLs you want to analyze after this prompt, and I will generate the JSON array following these specifications.

---

Input data:
Tags: [{"id":"0KTIQ8XLQNZ7NjLkAYXG","group":"CommonAttacks","name":"Replay Attack"},{"id":"0gjSQN9dAJQ22SJ9AbUP","name":"Portfolio","group":"CareerPath"},{"id":"1ZpOUrVCRnC2tctR2Dos","group":"HotTopics","name":"For Beginners"},{"id":"1p4d4NcqrDESeZ2Zj2mK","name":"Monitoring","group":"DeveloperInfrastructure"},{"id":"1qkCyKhzb4R2PvywFay3","name":"ERC-20","group":"ERC"},{"id":"2LYr7ETBRGMBsZylFLOK","group":"CareerPath","name":"Interview"},{"id":"2oaKAn3yRia9KwivRRXS","name":"DAO attacks","group":"SpecificAttackVectors"},{"id":"37j81peolsCPS7NFZPxH","group":"EIP","name":"EIP-1153"},{"id":"3BcULpb81i5PnSfkwfEv","name":"Reentrancy","group":"CommonAttacks"},{"id":"3L7TImBV3lL8emQekWsR","group":"DeFiAttacks","name":"DeFi Attacks thread"},{"id":"45KNwXbZn7DeL0FrT586","name":"EIP-4844","group":"EIP"},{"id":"4FgCNWJs5zNv6PJBLSJg","name":"EIP-1559","group":"EIP"},{"id":"4VOo7uOJSF1FJTZJQTCR","name":"Logic Attack","group":"SpecificAttackVectors"},{"id":"4huJ77CchTXKePBEL5oQ","name":"Hack Analysis","group":"HotTopics"},{"id":"4ucGDXToB9JSirKnnDLX","name":"Rounding error","group":"DeFiAttacks"},{"id":"4zf2u169OeWVxIjQFj6r","name":"Platforms","group":"CareerPath"},{"id":"56mmYOhZuGLoMRclYp7K","name":"Audit Summary","group":"CareerPath"},{"id":"5iuzJs977rpmNTjFafIN","group":"ERC","name":"ERC-2771"},{"id":"5o1eNwwICnXL7AAqv5AS","name":"Formal Verification","group":"Tests"},{"id":"5t38yISE8SUmYg3dGFj5","name":"Multichain","group":"CrossChain"},{"id":"6QOO5ikGfG8CvTtepJYn","group":"DeFiAttacks","name":"Rewards issue"},{"id":"6Z5kaWZ5huVEYcSU4JXD","name":"Common Attacks Thread","group":"CommonAttacks"},{"id":"6vW6wt9AusD4nZqXJmh4","group":"ERC","name":"Weird tokens"},{"id":"8v7DQgO6KBbg63qbGB53","name":"AI","group":"DeveloperInfrastructure"},{"id":"9rKQBuFwWF1TOjbAppju","group":"CommonAttacks","name":"Low Level Call Attacks"},{"id":"A12koI5ST471PSxr3422","group":"SpecificAttackVectors","name":"Input validation "},{"id":"AC1rQoPKjRtafrGYVJUD","group":"Tests","name":"Fuzzing"},{"id":"Aes8KjP90PuogF6jHjse","group":"Challenges","name":"Exercises"},{"id":"AlSZ9iLa4N08U6dWdMk6","name":"EIP-2535","group":"EIP"},{"id":"BCy99kIsPcff6W3wSUe0","name":"Bridge","group":"CrossChain"},{"id":"BFf2XPPKgJKC8NlLqqRv","group":"HotTopics","name":"Bug analysis"},{"id":"C5QWI6VOUCkjXxwzN2Qa","name":"Oracles","group":"BlockchainConceptsAndTechniques"},{"id":"CWQqmZ7rD27ypYj8UcGQ","name":"Foundry","group":"Tests"},{"id":"CoRMGPGivvzljGAvjKPL","name":"Cosmos Blockchain","group":"CrossChain"},{"id":"DKr1xdh3rIt0JvDF5QFP","group":"ERC","name":"ERC-7265"},{"id":"DROUrE58Ito1Z3D3vAF5","name":"Sandwitch Attack","group":"CommonAttacks"},{"id":"EnXEEcU4FJ8Q98fEVm83","name":"Checklists","group":"HotTopics"},{"id":"G3lzaQE8mSv8lZkX2nHq","name":"EIP-1167","group":"EIP"},{"id":"GTySlknepq7Hz3WhBGKb","group":"BlockchainConceptsAndTechniques","name":"Blockchain thread"},{"id":"Heblu6xlwam2xuZbyReD","group":"CrossChain","name":"Sui"},{"id":"HoFT8In21p0D3jQKZrhn","name":"ERC-1155","group":"ERC"},{"id":"HttYPmBNeYVpBnpOl637","group":"ERC","name":"ERC-4337"},{"id":"IpWcWz6WXNvSdZMZGgy3","group":"DeFi","name":"Uniswap"},{"id":"JtsYKSwwNeqpbcVRQOP7","group":"BlockchainConceptsAndTechniques","name":"MultiSig"},{"id":"Ka4hrWpckuYMpvEhNqi1","name":"Pools","group":"DeFi"},{"id":"L3k7kz6w56vWC0UI1ocf","name":"Libraries","group":"DeveloperInfrastructure"},{"id":"LE7I0nRNLElo3WfkWCJU","name":"ERC-7512","group":"ERC"},{"id":"LHVf7Z1MfyuG44j7nGdu","name":"Developer Tools","group":"DeveloperInfrastructure"},{"id":"MA6IJ3guocASszmK3xBG","group":"CommonAttacks","name":"Gas Griefing"},{"id":"MLfLntYg0AzCJP0eusHn","group":"Challenges","name":"CTF"},{"id":"MTcpTVk1QgX0cER1W1cy","name":"Phishing Attack","group":"CommonAttacks"},{"id":"N6nnaurtRIJJ1D8SRQUW","group":"Rest","name":"Math / Algo"},{"id":"N93ctNBeh4xdnAe1sZgE","group":"DeFiAttacks","name":"Collateral issue"},{"id":"NMkaqjuRvBUoTPh2IT9K","group":"DeveloperInfrastructure","name":"Transaction Explorer"},{"id":"Ob451v7FYrIN6VwsCiuV","group":"CommonAttacks","name":"DoS attack"},{"id":"PNr8PdU8KeAIpJpi9B7h","name":"Solana","group":"CrossChain"},{"id":"PYUvBBfOhliPpWpRWyls","name":"Project tutorials","group":"BlockchainConceptsAndTechniques"},{"id":"Pb6lAavov4EvY35K8NBL","group":"DeFi","name":"LSD"},{"id":"Phas2C09dCtI5dHOCLGa","name":"Math Attack","group":"SpecificAttackVectors"},{"id":"PjX7eaJDM08wb31seW0c","group":"DeFi","name":"DeFi Essentianls"},{"id":"PpSr1fb7uFNRF3xESZ20","group":"BlockchainConceptsAndTechniques","name":"Gas Optimization"},{"id":"PynSpmSm4xAipCHRoH7x","group":"DeFi","name":"DeFi Advanced"},{"id":"R2IyrwFgmPZPYOUhDPfc","group":"BlockchainConceptsAndTechniques","name":"Signatures"},{"id":"RM6W06ceEwVgmYLBtxa1","name":"Auditor Mindset","group":"CareerPath"},{"id":"RlRoI1MAKrPEEMSqu1IJ","name":"Options","group":"DeFi"},{"id":"SUcGbtvKytAKvi91CSE6","name":"ERC-3156","group":"ERC"},{"id":"UP83aSCDVmkCEsm9pgCl","name":"Zero Knowledge","group":"Rest"},{"id":"Ur0bP7CmQmTVu8h83HqV","name":"Overflow/Underflow Attack","group":"CommonAttacks"},{"id":"V3ahbBRnjwbjnriZkekS","group":"Rest","name":"Longread"},{"id":"V9B2u743lxnEtwSLglsE","group":"CommonAttacks","name":"Proxy Attack"},{"id":"VbNSPnBXbZdFUkVgCfI5","name":"Code analysis tool/lib","group":"DeveloperInfrastructure"},{"id":"WaQfOgfchOEYIq6UGkUT","group":"CommonAttacks","name":"Selfdestruct attack"},{"id":"X1rQY0nlQSQ4T71SMvJ5","name":"ERC-1363","group":"ERC"},{"id":"X2PsYaGcpjovEyLdQroC","group":"Rest","name":"ALL-IN-ONE"},{"id":"Y2UCqaI2W4hlsAtnec2E","name":"Ethernaut","group":"Challenges"},{"id":"Z0y9e7DpOr6zUT5Jhd9T","name":"TWAP","group":"DeFi"},{"id":"Z9olDozhwzuk9cNuQcMb","name":"Spot the Bug🐛","group":"HotTopics"},{"id":"ZLWPaY76TpMKdLeRDLmy","name":"ERC thread","group":"ERC"},{"id":"a750Jx0G311BHn9NAUm1","group":"EIP","name":"EIP-6963"},{"id":"aEThYHPpOQGMC69OLTBe","name":"Signature Database","group":"DeveloperInfrastructure"},{"id":"aVQywMtpDAah4e3Zwut6","group":"ERC","name":"ERC-777"},{"id":"aoFmtIlUT6ORD5npksMz","group":"DeFi","name":"Perpetuals"},{"id":"b3u0cV1FYVAc8PAiy1Ow","name":"⭐ SmartSecHub choice ⭐","group":"HotTopics"},{"id":"bHNj3a0VOsEfxAruyNs4","group":"BlockchainConceptsAndTechniques","name":"Code Templates"},{"id":"cWFrvDg3udtF4chqlRh7","group":"Tests","name":"POCs"},{"id":"dgFHXShRyXzZbT0ydyhk","group":"EIP","name":"EIP-3525"},{"id":"dvnswmE6aEvtFyONIHJQ","group":"CareerPath","name":"Contests/Bounties"},{"id":"eOW5aXSXLezQwBTa3Sbf","group":"DeFi","name":"CDP"},{"id":"eqjQzxbiZtU1rZrg5DsJ","name":"Bots","group":"Rest"},{"id":"fPAmOI5Bcgz4rtIs1cLh","name":"ERC-4626","group":"ERC"},{"id":"gNxWb2O2pCs4ESP3I1qY","name":"Flash Loan Attack","group":"DeFiAttacks"},{"id":"giCoYBP7mJ0LTSQpIaZ2","name":"Staking issues","group":"DeFiAttacks"},{"id":"giORPpbdsxXjVcNIdBnt","group":"DeFiAttacks","name":"Miscalculation issue"},{"id":"hGfCFQe7M9kq6MHgd47L","group":"DeFi","name":"AMM"},{"id":"hXA8feQxEBOaHA664C8G","name":"ERC-6900","group":"ERC"},{"id":"i6dAELk3SwqcaHeSEAtO","group":"BlockchainConceptsAndTechniques","name":"Proxies / Upgradable"},{"id":"iOWX23T1YTYFHJB5Fge2","name":"ERC-721","group":"ERC"},{"id":"iYf6A0I3f4jjQvuEDjC6","name":"Courses","group":"CareerPath"},{"id":"iwYxjmnmWAX7IxhHdyEq","name":"Ethereum / EVM codes","group":"BlockchainConceptsAndTechniques"},{"id":"j0xq1D6qMWVzEZgODTu0","group":"Challenges","name":"DamnVulnerableDeFi"},{"id":"jQsnNnWZXsMRvhSuj6sO","name":"Lending/Borrowing Attack","group":"DeFiAttacks"},{"id":"jSqSiqdmmjRV6F0moQ4b","name":"Threads","group":"HotTopics"},{"id":"kZts0zkNI6SzrEX35vJM","name":"Testing","group":"Tests"},{"id":"lMidBLH8oW76XK959oAj","group":"CommonAttacks","name":"Front Running attack"},{"id":"lrFOZ5lwV7cwiDV4hLTz","group":"DeFiAttacks","name":"Oracle Attacks"},{"id":"lvIvBCizzzf5cSNVRkVV","group":"DeFi","name":"Compound"},{"id":"nGT53tEvwoW0FbUrQ9cJ","name":"Solidity Essentials","group":"BlockchainConceptsAndTechniques"},{"id":"os7d1p4bToBA67WUS1br","name":"EIP-3074","group":"EIP"},{"id":"peIkxR4s0AUl50Mrtohh","group":"DeveloperInfrastructure","name":"Smart Contract reader/decoder"},{"id":"pyVp3LyGcsPuxu1EjOFe","name":"Randomness issue","group":"CommonAttacks"},{"id":"qIS7LSfRljRTH2xIXQOW","group":"BlockchainConceptsAndTechniques","name":"DAO / Governance"},{"id":"qpzyvhlsjIUafrBUxRi4","name":"NFT Attacks","group":"SpecificAttackVectors"},{"id":"r3TtkloePzTRW7QcMV0m","name":"ParadigmCTF","group":"Challenges"},{"id":"rT97p6Ga7IeLMJu5GgdX","name":"Layer 2","group":"BlockchainConceptsAndTechniques"},{"id":"rUUpZOLiiR6z78CJ3EdB","name":"DeFi thread","group":"DeFi"},{"id":"soMp3rG2bZnvWEUkRU7x","name":"Precision loss issue","group":"DeFiAttacks"},{"id":"tvaECi9G7ZHlNQOMJ4hb","group":"Rest","name":"Digest"},{"id":"tz1KDc2Q72FX9uq6qvMG","name":"Invariant Testing","group":"Tests"},{"id":"u64Tjgz1sAMitUE1aa7s","name":"Rust","group":"CrossChain"},{"id":"uI1sw2kJPoxTODU5LYIc","name":"Roadmaps","group":"CareerPath"},{"id":"uMOJxy0vN21Xbn5gt8VE","name":"Access Control issue","group":"CommonAttacks"},{"id":"uVZSKCOuMIMl0G7uYBwi","group":"BlockchainConceptsAndTechniques","name":"Defence/Design patterns"},{"id":"vH9apDSFhhaIdCKmWbBc","name":"Quiz","group":"Challenges"},{"id":"vsBw83D0KlshSS43O3mM","group":"EIP","name":"EIP-3448"},{"id":"wA5OodzeKZX2EyZzhdTW","name":"Live Audit","group":"CareerPath"},{"id":"wEEBKN1AyhJDRwQp05iF","name":"ERC-404","group":"ERC"},{"id":"wUOhOYBMohFRb0iPSiAq","name":"ERC-6551","group":"ERC"},{"id":"xy06rCsyLD9SUt0M6FQd","name":"Misc tools","group":"DeveloperInfrastructure"},{"id":"yK2hVFFQ9QM1alchG81u","group":"EIP","name":"EIP-2930"},{"id":"ymn1ZeB3zXJHIlhkAegR","name":"Slippage Attack","group":"DeFiAttacks"},{"id":"yzkgFzIyyz4FarC3LKTt","name":"Audit Report","group":"HotTopics"},{"id":"z1keIgxY6PqdGpyDascv","name":"Assembly/Yul","group":"BlockchainConceptsAndTechniques"},{"id":"z5FubOqsgDfodm0RnVBn","name":"Price Manipulation Attack","group":"DeFiAttacks"},{"id":"zYOoubPQ7OaV5l8BfPY3","group":"CrossChain","name":"Cross-Chain thread"},{"id":"zzgs9zWRYClGeQ8BHbBc","group":"CrossChain","name":"LayerZero"}]

list of author ids:

[{"id":"05O5N8FLvJVt2NbbS5bp","name":"Officer's Notes","twitter":"@officer_cia"},{"id":"0BuFbBg7xckDoikQIh1J","name":"Austin Griffith 🛠🚢🔥","twitter":"@austingriffith"},{"id":"0ocU5joMTCQvaK2om2IP","name":"Calyptus","twitter":"@calyptus_web3"},{"id":"17kJcdcNb1sN8X9m8J9a","name":"smlXL","twitter":"@smlxldotio"},{"id":"1DmsElFzqNbXPhkfox7E","name":"ImmuneBytes","twitter":"@ImmuneBytes"},{"id":"2EdvwDzkuhEE2wIY9Vc4","name":"zer0luck.eth","twitter":"@Younsle1"},{"id":"2L25356VAobKweejiNy0","name":"Quantstamp","twitter":"@Quantstamp"},{"id":"2VDAF5PXqVbdM23SwjiI","name":"SolidityScan","twitter":"@SolidityScan"},{"id":"2lXZ9Fa5xoNr1JcWuMAW","name":"RareSkills","twitter":"@RareSkills_io"},{"id":"2yRI5FZMEvFCD5oKs9HX","name":"OXORIO","twitter":"@0xorio"},{"id":"3MHWDcEnwE2ke3FSwVSr","name":"KALOS","twitter":"@kalos_security"},{"id":"3Y1qhISuIQHZGg8JQ588","name":"Beosin Alert","twitter":"@BeosinAlert"},{"id":"3cypCCJ3YCoaPOMgJnjE","name":"theredguild","twitter":"@theredguild"},{"id":"4D32PNprl5Kp9m3hrqkL","name":"Spearbit","twitter":"@SpearbitDAO"},{"id":"4SjArSMLVK6rRpEyLMlH","name":"Trust","twitter":"@trust__90"},{"id":"5z1h9rI8oBscdZSW92Ws","name":"@bytes032.xyz","twitter":"@bytes032"},{"id":"6IwTW1UxeR9Gcvq60MzR","name":"PeckShield Inc.","twitter":"@peckshield"},{"id":"6KippZDF9ta6SzijNHHu","name":"Maxwell ꓘ Dulin (Strikeout)","twitter":"https://x.com/Dooflin5"},{"id":"6wyyyx2zrwRzPZqDULv4","name":"cmichel.io","twitter":"@cmichelio"},{"id":"7w7cSFVNo4O6b1IUm624","name":"Composable Security","twitter":"@Composable_Sec"},{"id":"8g8GQTgdLp7QhoGA2PTi","name":"OpenZeppelin","twitter":"@OpenZeppelin"},{"id":"AsCUoBiwakngX9pqozEL","name":"MixBytes","twitter":"@MixBytes"},{"id":"BLEwMWxb4TT5MBghUCv6","name":"BlockApex","twitter":"@block_apex"},{"id":"BP8zavrYp5PanJnfpPwd","name":"Ackee Blockchain Security","twitter":"@AckeeBlockchain"},{"id":"BQLILU9V49cVCwBq9IIM","name":"Consensys Diligence","twitter":"@ConsensysAudits"},{"id":"Bdp2ioGyFMo91aoHOo2h","name":"truscova","twitter":"@truscova"},{"id":"BixJYvxWpauQLntA48JE","name":"Marq","twitter":"@marqymarq10"},{"id":"CHEb8JyOp6apO8sawyB7","name":"Halborn","twitter":"@HalbornSecurity"},{"id":"CJINUPyXDXUpaVXQz1rD","name":"Macro Security","twitter":"@0xMacroSecurity"},{"id":"CKcUpFbeyZl1qEZxzpcG","name":"Aitor Zaldua","twitter":"@azdraft_"},{"id":"CPT39j3tzx2TWqgc8qlP","name":"horsefacts","twitter":"@eth_call"},{"id":"CYTq8A6IoZMWoSkHz2uJ","name":"OpenSense","twitter":"@opensensepw"},{"id":"CauTqxcJaEZqLIAcin0D","name":"mrudenko","twitter":"@0xmrudenko"},{"id":"Cc4BSOqwJU8gLW3k77CL","name":"@0xVolodya","twitter":"@0xVolodya"},{"id":"CeWZCxZlykvoxkMyUyK6","name":"Blaize","twitter":"@blaize_tech"},{"id":"Cs9vfNMxfa8BPEb70SD5","name":"Ventral Digital","twitter":"@VentralDigital"},{"id":"EGhlYPbH0QkKPLgssAoa","name":"Omniscia","twitter":"@Omniscia_sec"},{"id":"F1QDecYFhJw0z8vExgPA","name":"Paradigm","twitter":"@paradigm"},{"id":"FRENBLq67h49OvGhBEbv","name":"Ethereum Foundation","twitter":"@ethereum"},{"id":"G8ICdwROZyMaVMwrkOkr","name":"DeGatchi","twitter":"@DeGatchi"},{"id":"GER07GFX7KSJjAI3jiny","name":"Inspex","twitter":"@InspexCo"},{"id":"GUO0HSRkdpfcbwpBCeWB","name":"ChainLight","twitter":"@ChainLight_io"},{"id":"HslqCvo0XF99YeVm50qk","name":"kebabsec","twitter":"@kebabsec"},{"id":"HtAJ4kAYSvRivUmiMRnF","name":"Heuss","twitter":"@UnoHeuss"},{"id":"IJXLVd529WAGczqdE09P","name":"Trail of Bits","twitter":"@trailofbits"},{"id":"ILk1IQP5cmqA8ZZm0xun","name":"🐸Smart🐸Contract🐸Programmer🐸","twitter":"@ProgrammerSmart"},{"id":"J7eyTmxI3LDxal0mTf3x","name":"MiloTruck","twitter":"@milotruck"},{"id":"JvjXhsGWf4Nq2AG7OAi1","name":"BountyHunt3r","twitter":"@Bount3yHunt3r"},{"id":"K358lqYhtuQRWzBucwIn","name":"SHERLOCK","twitter":"@sherlockdefi"},{"id":"KM1rNklnMzfmIBMHIm0i","name":"hexens","twitter":"@hexensio"},{"id":"KOJVqUgPZumMnpBEESCR","name":"Certik","twitter":"@CertiK"},{"id":"KxIwGurJ2ED3LzNGWMKD","name":"HollaDieWaldfee","twitter":"@HollaWaldfee100"},{"id":"L0gXuPHQZSBCpYaEQiv8","name":"Dedaub","twitter":"@dedaub"},{"id":"MJNWduIJ08B6pwbxxegA","name":"ChainSecurity","twitter":"@chain_security"},{"id":"NPDcjFucPzGvwsKun0eO","name":"Veridise","twitter":"@VeridiseInc"},{"id":"OIU5cv4VWVMsIrkdDCCN","name":"George Hunter","twitter":"@GeorgeHNTR"},{"id":"OLVUYT2VDt79e7ChhuUm","name":"Olympix","twitter":"@Olympix_ai"},{"id":"OwLesOCiBtW9omAjykJx","name":"Callisto Network Official","twitter":"@CallistoSupport"},{"id":"P4nvlbribJIRSIygkw97","name":"Immunefi","twitter":"@immunefi"},{"id":"Q6iugNd6vQRpUCMZT1cv","name":"Zellic","twitter":"@zellic_io"},{"id":"Qc3e35ktJYJP16KVofLb","name":"matta @ The Red Guild 🪷","twitter":"@mattaereal"},{"id":"T2r7OEcOqPgN9oDQOdX1","name":"Zaryab","twitter":"@zaryab_eth"},{"id":"THq0rdl7FVbupGKoJAWp","name":"zokyo 🇺🇦","twitter":"@zokyo_io"},{"id":"TcBml4on1UBXkZp8wai0","name":"HACxyk.","twitter":"@Hacxyk"},{"id":"TgROws05K461fFRgx43O","name":"Rektoff","twitter":"@rektoff_xyz"},{"id":"TpW247bPPIh1smSVDZFz","name":"Guillaume Lambert | lambert.eth | 🦇🔊","twitter":"@guil_lambert"},{"id":"VKKrpG91IT1mebaQLiSo","name":"StErMi","twitter":"@StErMi"},{"id":"Wf5eNyGjYzqddrNtdiF4","name":"SlowMist","twitter":"@SlowMist_Team"},{"id":"WyTElcqFIIj28hUIZrzK","name":"Decurity","twitter":"@DecurityHQ"},{"id":"XbB4XWANMljecus2XgE6","name":"Markus Waas","twitter":"@gorgos"},{"id":"Xczvbx7LJhbbzNQhoBpz","name":"Web3 Security News","twitter":"@web3sec_news"},{"id":"XvuTaF2KZSsyE2ZiTCfK","name":"dravee.eth","twitter":"@BowTiedDravee"},{"id":"XzwdfRufex6OXDBLE5Fi","name":"Hacken🇺🇦","twitter":"@hackenclub"},{"id":"YK4I8jk8NqwMg3jCzXhW","name":"jeiwan.eth 🦇","twitter":"@jeiwan7"},{"id":"aXgzApo2SzARMAYlY194","name":"HashEx DeFi Intelligence","twitter":"@HashExOfficial"},{"id":"aa9yzb3ippGflklNXcEm","name":"Sigma Prime","twitter":"@sigp_io"},{"id":"aodGGrMIjMhJUA3woXCd","name":"SunSec","twitter":"@1nf0s3cpt"},{"id":"bf0qGKiZxIr3s34XAg3T","name":"Oak Security","twitter":"@SecurityOak"},{"id":"bfMcmoLyfaJtAFcIBtlM","name":"Sec3","twitter":"@sec3dev"},{"id":"bubLF1HXxKq3cyaOHVz7","name":"Fuzzy","twitter":"@fuzzy_fyi"},{"id":"cQAqSNhaioMME2w146sv","name":"J4X","twitter":"@J4X_98"},{"id":"cuQaX2AMArsADqzUIlKz","name":"kaden.eth","twitter":"@0xKaden"},{"id":"dFcKfe33OtOFYT2xIK2Q","name":"ddimitrov22","twitter":"@ddimitrovv22"},{"id":"dOjGjPR7mqXPuUBkv5fF","name":"Alex Babits","twitter":"@alexbabits"},{"id":"dfy8cq00ARVJ96vqcbjs","name":"Alchemy | Powering Web3⚡️","twitter":"@AlchemyPlatform"},{"id":"dzlUhFuMZOlSBPaXVaCY","name":"Recon","twitter":"https://x.com/getreconxyz"},{"id":"e4Ro5F3zD7JQLjHVYmLE","name":"Jose María De la Cruz","twitter":"@0xjmaria"},{"id":"ekfY4JTiM9gPeKUFNCuj","name":"FuzzingLabs","twitter":"@FuzzingLabs"},{"id":"fbR3jh1hOsq426LvLNTk","name":"Beirao.xyz | Recudizoor 🕵️‍♂️","twitter":"@0xBeirao"},{"id":"feUavaXW14uCbeAvkfc3","name":"Dimitar Tsvetanov","twitter":"@cvetanovv0"},{"id":"iTzlmQFe2OWH4oiHckfT","name":"33Audits","twitter":"@solidityauditor"},{"id":"ifdrAvonDLXsKn5z6hPv","name":"Numen Cyber","twitter":"@numencyber"},{"id":"jn3vxaukjZNTtXIglXb6","name":"SΞCURΞUM","twitter":"@TheSecureum"},{"id":"jrhWYURRDZOj8IlevtOH","name":"Sayfer","twitter":"@SayferSecurity"},{"id":"lH19YrtljbzCcJx57x3V","name":"BlockSec","twitter":"@BlockSecTeam"},{"id":"lNqf3bdR1fm1kDtZMduE","name":"PWNING","twitter":"@PwningEth"},{"id":"larUtAkmmWnnzHSzDLJv","name":"Johny Time","twitter":"@RealJohnnyTime"},{"id":"mDXXnVAwY1oDrgG5dbZc","name":"Certora","twitter":"@CertoraInc"},{"id":"mt9gD4taBAAfqIsLrNEn","name":"SharkTeam","twitter":"@sharkteamorg"},{"id":"obxfV0mcOnyvmtLwVQY9","name":"Dacian","twitter":"@DevDacian"},{"id":"owWD7CnVbUiatJVPTPhe","name":"bloqarl","twitter":"@TheBlockChainer"},{"id":"pAuU5QIk0eStI1nZIaao","name":"Valix Consulting","twitter":"@ValixConsulting"},{"id":"pJjqWX9EMapP4vz0tHFu","name":"AuditOne","twitter":"@auditone_team"},{"id":"pMgW9Uh6bVoXLyB2B72X","name":"Eocene | Security","twitter":"@EoceneSecurity"},{"id":"qYgumReaZdMQ0mqo8kcQ","name":"Shieldify Security","twitter":"https://x.com/ShieldifySec"},{"id":"qxJDfowcIDj0eLu35rav","name":"serial-coder (Phuwanai Thummavet)","twitter":"@0x_serial_coder"},{"id":"tIZjtkjhqzNhNvRofdnj","name":"Arbitrary Execution","twitter":"@Arbitrary_Exec"},{"id":"tV760wRSy4fszaQ6fcBD","name":"Akshay Srivastav","twitter":"@akshaysrivastv"},{"id":"tsZG58oCNqcnqJnSs5Je","name":"iosiro","twitter":"@iosiro_security"},{"id":"viaPHmA9Zmq0zaHvOp0m","name":"Runtime Vеrification","twitter":"@rv_inc"},{"id":"vrx6zCcxalz1GJqq9ToF","name":"Owen | Guardian Audits 🛡️","twitter":"@0xOwenThurm"},{"id":"wCCWpTRTYDxB63bGDXz7","name":"Code4rena","twitter":"@code4rena"},{"id":"wDgAbrr4oM3zJiwC9gTd","name":"Antonio Viggiano","twitter":"@agfviggiano"},{"id":"wJ3wSnZUyaUTViawdYWv","name":"QuillAudits ","twitter":"@quillaudits_ai"},{"id":"xBCwQPoNqNNebqp66bwh","name":"Audita","twitter":"@AuditaSecurity"},{"id":"xGPLXYdLzfpnhjTY041N","name":"Lossless","twitter":"@losslessdefi"},{"id":"xz4NJe0c4MkxxfOWsUfB","name":"Uri Kirstein","twitter":"@KirsteinUri"},{"id":"y0yw20WTl54bUZ1P3ifE","name":"Guardian Audits 🛡","twitter":"@GuardianAudits"},{"id":"ye84kBumBtofDWaVYybs","name":"Web3 University","twitter":"@web3university"},{"id":"zTalP18jdtM22CND06Ae","name":"Chainlink","twitter":"@chainlink"},{"id":"zljwUffSKJTWUbPFzi5g","name":"CoinFabrik","twitter":"@coinfabrik"},{"id":"zsFNjFWBVapIKy3MeoEs","name":"deliriusz","twitter":"@deliriusz_eth"}]

author id - get from list 
Twitter ID - @HalbornSecurity

Links:

https://www.halborn.com/blog/post/explained-the-alex-protocol-hack-june-2025
https://www.youtube.com/watch?v=enqeYjf12hY
https://www.halborn.com/blog/post/explained-the-force-bridge-hack-june-2025
https://www.halborn.com/blog/post/explained-the-cork-protocol-hack-may-2025
https://www.youtube.com/watch?v=Wf41C9dVp7g
https://www.halborn.com/blog/post/explained-the-cetus-hack-may-2025
https://www.halborn.com/blog/post/explained-the-lnd-hack-may-2025
https://www.youtube.com/watch?v=gTqSyOHbAvs
https://www.halborn.com/blog/post/explained-the-coinbase-extortion-attack-may-2025
https://www.halborn.com/blog/post/explained-the-mobius-hack-may-2025
https://www.halborn.com/blog/post/explained-coinbase-users-phishing-attacks-may-2025
https://www.halborn.com/blog/post/explained-the-loopscale-hack-april-2025
https://www.halborn.com/blog/post/stablecoins-explained-pegging-models-depegging-risks-and-security-threats
https://www.youtube.com/watch?v=SUUEcSGrjMQ
https://www.halborn.com/blog/post/explained-the-zksync-hack-april-2025
https://www.halborn.com/blog/post/explained-the-kiloex-hack-april-2025
https://www.halborn.com/blog/post/explained-the-hyperliquid-hack-march-2025
https://www.halborn.com/blog/post/explained-the-abracadabra-money-hack-march-2025
https://www.halborn.com/blog/post/explained-the-zoth-hack-march-2025
https://www.youtube.com/watch?v=8QWeFiAT1AA
https://www.halborn.com/blog/post/explained-the-wemix-hack-march-2025
https://www.youtube.com/watch?v=CtDTWxto8To
https://www.halborn.com/blog/post/explained-the-1inch-hack-march-2025
https://www.youtube.com/watch?v=3k6-4setUNM
https://www.halborn.com/blog/post/explained-the-bybit-hack-february-2025
https://www.halborn.com/blog/post/explained-the-zklend-hack-february-2025
https://www.halborn.com/blog/post/explained-the-ionic-money-hack-february-2025
https://www.halborn.com/blog/post/month-in-review-top-defi-hacks-of-january-2025
https://www.halborn.com/blog/post/explained-the-adspower-hack-january-2025
https://www.halborn.com/blog/post/explained-the-phemex-hack-january-2025
https://www.youtube.com/watch?v=2fz1GokwAWc
